package logic

import (
	"context"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典
func (l *DeleteDictLogic) DeleteDict(in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	// todo: add your logic here and delete this line

	return &dict.DeleteDictResp{}, nil
}
