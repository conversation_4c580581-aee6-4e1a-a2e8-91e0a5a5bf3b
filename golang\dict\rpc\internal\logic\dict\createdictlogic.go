package dict

import (
	"context"
	"errors"
	"time"

	"rpc/dict"
	"rpc/internal/svc"
	"rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ===== 字典管理 =====
func (l *CreateDictLogic) CreateDict(in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	// 参数验证
	if in.Code == "" {
		return nil, errors.New("字典编码不能为空")
	}
	if in.Name == "" {
		return nil, errors.New("字典名称不能为空")
	}

	// 检查编码是否已存在
	exists, err := l.svcCtx.DictModel.CheckCodeExists(in.Code, 0)
	if err != nil {
		l.Logger.Errorf("检查字典编码是否存在失败: %v", err)
		return nil, errors.New("检查字典编码失败")
	}
	if exists {
		return nil, errors.New("字典编码已存在")
	}

	// 创建字典数据
	dictData := &model.Dict{
		Code:        in.Code,
		Name:        in.Name,
		Remark:      in.Remark,
		Status:      in.Status,
		CreatedTime: time.Now(),
		UpdatedTime: time.Now(),
	}

	// 保存到数据库
	err = l.svcCtx.DictModel.Create(dictData)
	if err != nil {
		l.Logger.Errorf("创建字典失败: %v", err)
		return nil, errors.New("创建字典失败")
	}


	return &dict.CreateDictResp{
		Id:      dictData.ID,
		Message: "创建字典成功",
	}, nil
}
