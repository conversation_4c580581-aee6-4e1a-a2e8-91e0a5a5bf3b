package logic

import (
	"context"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictLogic {
	return &GetDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典详情
func (l *GetDictLogic) GetDict(in *dict.GetDictReq) (*dict.GetDictResp, error) {
	// todo: add your logic here and delete this line

	return &dict.GetDictResp{}, nil
}
