// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict.proto

package dictsystemservice

import (
	"context"

	"rpc/dict"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateDictCategoryReq  = dict.CreateDictCategoryReq
	CreateDictCategoryResp = dict.CreateDictCategoryResp
	CreateDictItemReq      = dict.CreateDictItemReq
	CreateDictItemResp     = dict.CreateDictItemResp
	CreateDictReq          = dict.CreateDictReq
	CreateDictResp         = dict.CreateDictResp
	DeleteDictCategoryReq  = dict.DeleteDictCategoryReq
	DeleteDictCategoryResp = dict.DeleteDictCategoryResp
	DeleteDictItemReq      = dict.DeleteDictItemReq
	DeleteDictItemResp     = dict.DeleteDictItemResp
	DeleteDictReq          = dict.DeleteDictReq
	DeleteDictResp         = dict.DeleteDictResp
	Dict                   = dict.Dict
	DictCategory           = dict.DictCategory
	DictItem               = dict.DictItem
	GetDictCategoryReq     = dict.GetDictCategoryReq
	GetDictCategoryResp    = dict.GetDictCategoryResp
	GetDictItemReq         = dict.GetDictItemReq
	GetDictItemResp        = dict.GetDictItemResp
	GetDictReq             = dict.GetDictReq
	GetDictResp            = dict.GetDictResp
	ListDictCategoryReq    = dict.ListDictCategoryReq
	ListDictCategoryResp   = dict.ListDictCategoryResp
	ListDictItemReq        = dict.ListDictItemReq
	ListDictItemResp       = dict.ListDictItemResp
	ListDictReq            = dict.ListDictReq
	ListDictResp           = dict.ListDictResp
	UpdateDictCategoryReq  = dict.UpdateDictCategoryReq
	UpdateDictCategoryResp = dict.UpdateDictCategoryResp
	UpdateDictItemReq      = dict.UpdateDictItemReq
	UpdateDictItemResp     = dict.UpdateDictItemResp
	UpdateDictReq          = dict.UpdateDictReq
	UpdateDictResp         = dict.UpdateDictResp

	DictSystemService interface {
		// ===== 字典管理 =====
		CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error)
		// 更新字典
		UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error)
		// 删除字典
		DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error)
		// 获取字典详情
		GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error)
		// 字典列表
		ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error)
		// ===== 字典分类管理 =====
		CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error)
		// 更新字典分类
		UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error)
		// 删除字典分类
		DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error)
		// 获取字典分类详情
		GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error)
		// 字典分类列表
		ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error)
		// ===== 字典项管理 =====
		CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error)
		// 更新字典项
		UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error)
		// 删除字典项
		DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error)
		// 获取字典项详情
		GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error)
		// 字典项列表
		ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error)
	}

	defaultDictSystemService struct {
		cli zrpc.Client
	}
)

func NewDictSystemService(cli zrpc.Client) DictSystemService {
	return &defaultDictSystemService{
		cli: cli,
	}
}

// ===== 字典管理 =====
func (m *defaultDictSystemService) CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.CreateDict(ctx, in, opts...)
}

// 更新字典
func (m *defaultDictSystemService) UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.UpdateDict(ctx, in, opts...)
}

// 删除字典
func (m *defaultDictSystemService) DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.DeleteDict(ctx, in, opts...)
}

// 获取字典详情
func (m *defaultDictSystemService) GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.GetDict(ctx, in, opts...)
}

// 字典列表
func (m *defaultDictSystemService) ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.ListDict(ctx, in, opts...)
}

// ===== 字典分类管理 =====
func (m *defaultDictSystemService) CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.CreateDictCategory(ctx, in, opts...)
}

// 更新字典分类
func (m *defaultDictSystemService) UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.UpdateDictCategory(ctx, in, opts...)
}

// 删除字典分类
func (m *defaultDictSystemService) DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.DeleteDictCategory(ctx, in, opts...)
}

// 获取字典分类详情
func (m *defaultDictSystemService) GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.GetDictCategory(ctx, in, opts...)
}

// 字典分类列表
func (m *defaultDictSystemService) ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.ListDictCategory(ctx, in, opts...)
}

// ===== 字典项管理 =====
func (m *defaultDictSystemService) CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.CreateDictItem(ctx, in, opts...)
}

// 更新字典项
func (m *defaultDictSystemService) UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.UpdateDictItem(ctx, in, opts...)
}

// 删除字典项
func (m *defaultDictSystemService) DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.DeleteDictItem(ctx, in, opts...)
}

// 获取字典项详情
func (m *defaultDictSystemService) GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.GetDictItem(ctx, in, opts...)
}

// 字典项列表
func (m *defaultDictSystemService) ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error) {
	client := dict.NewDictSystemServiceClient(m.cli.Conn())
	return client.ListDictItem(ctx, in, opts...)
}
