package svc

import (
	"api/internal/config"
	"dict_rpc/dictservice"
	"dict_category_rpc/dictcategoryservice"
	"dict_item_rpc/dictitemservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config  config.Config
	DictRpc dictservice.DictService
	DictCategoryRpc 
	DictItemRpc dictsystemservice.DictSystemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:  c,
		DictRpc: dictsystemservice.NewDictSystemService(zrpc.MustNewClient(c.DictRpc)),
		DictRpc: dictsystemservice.NewDictSystemService(zrpc.MustNewClient(c.DictRpc)),
		DictRpc: dictsystemservice.NewDictSystemService(zrpc.MustNewClient(c.DictRpc)),
	}
}
