package model

import (
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// Dict 字典模型
type Dict struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	Code        string         `gorm:"type:varchar(50);uniqueIndex;not null"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Remark      string         `gorm:"type:varchar(500)"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (Dict) TableName() string {
	return "dict"
}

// DictModel 字典模型结构
type DictModel struct {
	db *gorm.DB
}

// NewDictModel 创建字典模型
func NewDictModel(db *gorm.DB) *DictModel {
	return &DictModel{
		db: db,
	}
}

// FindByID 根据ID查询字典
func (m *DictModel) FindByID(id int64) (*Dict, error) {
	var dict Dict
	result := m.db.First(&dict, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典id=" + strconv.Itoa(int(id)) + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// FindByCode 根据编码查询字典
func (m *DictModel) FindByCode(code string) (*Dict, error) {
	var dict Dict
	result := m.db.Where("code = ?", code).First(&dict)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典编码=" + code + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// FindByCode 根据编码查询字典
func (m *DictModel) FindByName(name string) (*Dict, error) {
	var dict Dict
	result := m.db.Where("name = ?", name).First(&dict)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典名称=" + name + "不存在")
		}
		return nil, result.Error
	}
	return &dict, nil
}

// Create 创建字典
func (m *DictModel) Create(dict *Dict) error {
	return m.db.Create(dict).Error
}

// Update 更新字典
func (m *DictModel) Update(dict *Dict) error {
	return m.db.Save(dict).Error
}

// Delete 删除字典
func (m *DictModel) Delete(id int64) error {
	return m.db.Delete(&Dict{}, id).Error
}

// List 分页查询字典列表
func (m *DictModel) List(page, pageSize int32, code, name string, status int32) ([]*Dict, int64, error) {
	var dicts []*Dict
	var total int64

	query := m.db.Model(&Dict{})

	// 条件查询
	if code != "" {
		query = query.Where("code LIKE ?", "%"+code+"%")
	}
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if status != -1 {
		query = query.Where("status = ?", status)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := int((page - 1) * pageSize)
	if err := query.Offset(offset).Limit(int(pageSize)).Order("created_time DESC").Find(&dicts).Error; err != nil {
		return nil, 0, err
	}

	return dicts, total, nil
}

// CheckCodeExists 检查编码是否存在
func (m *DictModel) CheckCodeExists(code string, excludeID int64) (bool, error) {
	var count int64
	query := m.db.Model(&Dict{}).Where("code = ?", code)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	if err := query.Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
